using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class MemberDetailWithHistoryDto
    {
        public int MemberID { get; set; }
        public string Name { get; set; }
        public byte Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string Adress { get; set; }
        public DateOnly? BirthDate { get; set; }
        public string Email { get; set; }
        public bool? IsActive { get; set; }
        public string ScanNumber { get; set; }
        public decimal Balance { get; set; }
        public DateTime? CreationDate { get; set; }
        public int? RemainingDays { get; set; } // <PERSON>lan gün sayısı eklendi
        public DateTime? LastMembershipEndDate { get; set; } // Son üyelik bitiş tarihi
        
        // Membership history
        public List<MembershipHistoryDto> Memberships { get; set; }
        
        // Payment history
        public List<PaymentHistoryItemDto> Payments { get; set; }
        
        // Entry/Exit history
        public List<EntryExitHistoryItemDto> EntryExitHistory { get; set; }
        
        // Membership freeze history
        public List<MembershipFreezeHistoryItemDto> FreezeHistory { get; set; }
    }

    public class MembershipHistoryDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string TypeName { get; set; }
        public string Branch { get; set; }
        public int Day { get; set; }
        public decimal Price { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IsActive { get; set; }
        public bool IsFrozen { get; set; }
        public DateTime? FreezeStartDate { get; set; }
        public DateTime? FreezeEndDate { get; set; }
        public int? FreezeDays { get; set; }
        public DateTime? OriginalEndDate { get; set; }
        public DateTime? CreationDate { get; set; }
    }

    public class PaymentHistoryItemDto
    {
        public int PaymentID { get; set; }
        public int MembershipID { get; set; }
        public string MembershipTypeName { get; set; }
        public string Branch { get; set; }
        public DateTime PaymentDate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; }
        public string PaymentStatus { get; set; }
    }

    public class EntryExitHistoryItemDto
    {
        public int EntryExitID { get; set; }
        public int MembershipID { get; set; }
        public string MembershipTypeName { get; set; }
        public string Branch { get; set; }
        public DateTime? EntryDate { get; set; }
        public DateTime? ExitDate { get; set; }
    }

    public class MembershipFreezeHistoryItemDto
    {
        public int FreezeHistoryID { get; set; }
        public int MembershipID { get; set; }
        public string MembershipTypeName { get; set; }
        public string Branch { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime PlannedEndDate { get; set; }
        public DateTime? ActualEndDate { get; set; }
        public int FreezeDays { get; set; }
        public int? UsedDays { get; set; }
        public string CancellationType { get; set; }
    }
}
