using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Caching
{
    public class DebugCacheRemoveAspect : MethodInterception
    {
        private string _pattern;
        private ICacheManager _cacheManager;
        private ICompanyContext _companyContext;

        public DebugCacheRemoveAspect(string pattern)
        {
            _pattern = pattern;
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        protected override void OnSuccess(IInvocation invocation)
        {
            // Log method information
            Debug.WriteLine($"DebugCacheRemoveAspect - Method: {invocation.Method.DeclaringType.FullName}.{invocation.Method.Name}");
            Debug.WriteLine($"DebugCacheRemoveAspect - Original Pattern: {_pattern}");
            
            // Şirket ID'sini al
            int companyId = -1;
            if (_companyContext != null)
            {
                companyId = _companyContext.GetCompanyId();
            }
            Debug.WriteLine($"DebugCacheRemoveAspect - Company ID: {companyId}");

            // Try different pattern approaches
            
            // 1. Original approach with company ID
            string companyPattern = $"Company_{companyId}_{_pattern}";
            Debug.WriteLine($"DebugCacheRemoveAspect - Using pattern 1: {companyPattern}");
            _cacheManager.RemoveByPattern(companyPattern);
            
            // 2. More general pattern with just the class name
            string classNamePattern = $".*{invocation.Method.DeclaringType.Name}.*";
            Debug.WriteLine($"DebugCacheRemoveAspect - Using pattern 2: {classNamePattern}");
            _cacheManager.RemoveByPattern(classNamePattern);
            
            // 3. Very specific pattern with the exact method name
            string methodPattern = $".*{invocation.Method.DeclaringType.FullName}.{invocation.Method.Name}.*";
            Debug.WriteLine($"DebugCacheRemoveAspect - Using pattern 3: {methodPattern}");
            _cacheManager.RemoveByPattern(methodPattern);
            
            // 4. Pattern with just the company ID
            string companyOnlyPattern = $"Company_{companyId}_.*";
            Debug.WriteLine($"DebugCacheRemoveAspect - Using pattern 4: {companyOnlyPattern}");
            _cacheManager.RemoveByPattern(companyOnlyPattern);
        }
    }
}
