﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class ProductManager : IProductService
    {
        IProductDal _productDal;

        public ProductManager(IProductDal productDal)
        {
            _productDal = productDal;
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        //[DebugCacheRemoveAspect("Business.Concrete.ProductManager.GetAll")]
        public IResult Add(Product product)
        {
            _productDal.Add(product);
            return new SuccessResult(Messages.ProductAdded);
        }
        [LogAspect]
        [SecuredOperation("owner,admin")]
        //[DebugCacheRemoveAspect("Business.Concrete.ProductManager.GetAll")]
        public IResult Delete(int productId)
        {

            _productDal.Delete(productId);
            return new SuccessResult(Messages.ProductDeleted);
        }
        [SecuredOperation("owner,admin")]
        //[CacheAspect(duration: 60)] 
        public IDataResult<List<Product>> GetAll()
        {
            return new SuccessDataResult<List<Product>>(_productDal.GetAll(m => m.IsActive == true));

        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        public IDataResult<Product> GetById(int productId)
        {
            return new SuccessDataResult<Product>(_productDal.Get(p => p.ProductID == productId));
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        //[DebugCacheRemoveAspect("Business.Concrete.ProductManager.GetAll")]
        public IResult Update(Product product)
        {
            _productDal.Update(product);
            return new SuccessResult(Messages.ProductUpdated);
        }
    }
}
