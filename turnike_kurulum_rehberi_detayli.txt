# TURNİKE KURULUM REHBERİ - SIFIRDAN BAŞLANGIÇ
## Elektronik Bilgisi Olmayan Kullanıcılar İçin Detaylı Kılavuz

Bu rehber, spor salonu yönetim sisteminizde Raspberry Pi ile turnike kontrolü kurmanız için hazırlanmıştır. Her adım, elektronik bilgisi olmayan kullanıcılar için en basit şekilde açıklanmıştır.

---

## 1. SİSTEM GENEL BAKIŞ

### Sisteminizin Yapısı:
- **Backend:** C# ile yazılmış API (api.gymkod.com)
- **Frontend:** Angular ile yazılmış yönetim paneli
- **Mobil:** Üye QR kod uygulaması
- **Raspberry Pi:** <PERSON>ike kontrolü (Python ile)

### Nasıl Çalışır:
1. Üye QR kodunu okuttucu cihaza gösterir
2. Raspberry Pi QR kodu okur
3. API'ye üye bilgilerini sorar
4. Üyelik geçerliyse turnikeyi açar
5. 5 saniye sonra turnike kapanır

---

## 2. İHTİYAÇ LİSTESİ

### Donanım:
- ✅ Raspberry Pi 4 (elinizde var)
- ✅ MicroSD Kart 32GB+ (elinizde var)
- ✅ Monitör ve HDMI kablosu (elinizde var)
- ❌ **1 Kanallı Röle Modülü** (satın alınacak)
- ❌ **3 adet Erkek-Dişi Jumper Kablo** (satın alınacak)
- ❌ **Multimetre** (test için, isteğe bağlı)

### Yazılım:
- ✅ Raspberry Pi OS (kurulu)
- ✅ Python kodu (elinizde var)

---

## 3. RÖLE MODÜLÜ SATIN ALMA REHBERİ

### Hangi Röle Modülünü Almalısınız?

**Aranacak Özellikler:**
- 1 Kanallı Röle Modülü
- 5V kontrol voltajı
- Optik izolasyonlu
- Arduino/Raspberry Pi uyumlu
- SRD-05VDC-SL-C röle içeren

**Önerilen Mağazalar:**
- Robotistan.com
- Direnc.net
- Amazon Türkiye
- Gittigidiyor

**Fiyat Aralığı:** 15-30 TL

**Ürün Örneği Arama Kelimeleri:**
"1 kanallı röle modülü raspberry pi"
"5v relay module arduino"

---

## 4. JUMPER KABLO SATIN ALMA

**İhtiyacınız Olan:**
- 3 adet Erkek-Dişi (Male-Female) Jumper Kablo
- Farklı renklerde (Kırmızı, Siyah, Sarı/Yeşil)
- 20cm uzunluk yeterli

**Fiyat:** 5-10 TL (genellikle 40'lı paket halinde satılır)

---

## 5. RASPBERRY Pi PIN HARİTASI

```
Raspberry Pi 4 - 40 Pin GPIO Haritası

     3.3V  [ 1] [ 2]  5V     ← Pin 2: Güç (Kırmızı kablo)
    GPIO2  [ 3] [ 4]  5V
    GPIO3  [ 5] [ 6]  GND    ← Pin 6: Toprak (Siyah kablo)
    GPIO4  [ 7] [ 8]  GPIO14
      GND  [ 9] [10]  GPIO15
   GPIO17  [11] [12]  GPIO18 ← Pin 12: Kontrol (Sarı kablo)
   GPIO27  [13] [14]  GND
   GPIO22  [15] [16]  GPIO23
     3.3V [17] [18]  GPIO24
   GPIO10  [19] [20]  GND
    GPIO9  [21] [22]  GPIO25
   GPIO11  [23] [24]  GPIO8
      GND [25] [26]  GPIO7
    GPIO0  [27] [28]  GPIO1
    GPIO5  [29] [30]  GND
    GPIO6  [31] [32]  GPIO12
   GPIO13  [33] [34]  GND
   GPIO19  [35] [36]  GPIO16
   GPIO26  [37] [38]  GPIO20
      GND [39] [40]  GPIO21
```

---

## 6. RÖLE MODÜLÜ PIN AÇIKLAMALARI

### Röle Modülü Üzerindeki Pinler:

**Kontrol Tarafı (Raspberry Pi'ye bağlanacak):**
- **VCC:** Güç girişi (5V) - Kırmızı kablo
- **GND:** Toprak - Siyah kablo  
- **IN:** Kontrol sinyali - Sarı/Yeşil kablo

**Röle Çıkış Tarafı (Turnikeye bağlanacak):**
- **COM:** Ortak pin
- **NO:** Normally Open (Normal durumda açık)
- **NC:** Normally Closed (Normal durumda kapalı)

### Hangi Pinleri Kullanacağız?
**COM ve NO** pinlerini kullanacağız. Çünkü:
- Normal durumda devre açık
- Sinyal geldiğinde devre kapanır
- Turnike tetiklenir

---

## 7. ADIM ADIM BAĞLANTI ŞEMASI

### Adım 1: Güvenlik Önlemleri
```
⚠️ KRİTİK UYARILAR:
- Raspberry Pi kapalı olmalı
- Turnike gücü kesilmiş olmalı
- Kablolar karışmamalı
- Çift kontrol yapın
```

### Adım 2: Raspberry Pi Bağlantıları

**Kablo Renk Kodları:**
```
Röle Modülü    →    Raspberry Pi        →    Kablo Rengi
VCC            →    Pin 2 (5V)          →    KIRMIZI
GND            →    Pin 6 (GND)         →    SİYAH
IN             →    Pin 12 (GPIO 18)    →    SARI/YEŞİL
```

### Adım 3: Görsel Bağlantı Kontrolü

**Raspberry Pi'ye bakış açısı (USB portları size doğru):**
```
Pin 1 (3.3V)  ●  ● Pin 2 (5V) ← KIRMIZI KABLO
Pin 3         ●  ● Pin 4
Pin 5         ●  ● Pin 6 (GND) ← SİYAH KABLO  
Pin 7         ●  ● Pin 8
Pin 9         ●  ● Pin 10
Pin 11        ●  ● Pin 12 (GPIO18) ← SARI KABLO
```

---

## 8. TURNİKE BAĞLANTILARI

### Turnike Kontrol Kutusunu Bulma

**Aranacak Yerler:**
1. Turnikenin alt kısmında metal kutu
2. Turnikenin yan tarafında plastik kutu
3. Duvarda turnike yakınında kutu

**Kutuda Aranacak Etiketler:**
- "ACCESS CONTROL"
- "DRY CONTACT" 
- "OPEN/CLOSE"
- "UNLOCK/LOCK"
- "+" ve "-" işaretleri

### Yaygın Turnike Kablo Renkleri:
- **Kırmızı/Kahverengi:** + (Pozitif)
- **Siyah/Mavi:** - (Negatif)
- **Yeşil/Sarı:** Kontrol sinyali

### Bağlantı Şeması:
```
Röle Modülü          Turnike Kontrol
COM pin       →      + (Pozitif kablo)
NO pin        →      - (Negatif kablo)
```

---

## 9. YAZILIM KURULUMU

### Mevcut Kodunuzun Analizi:

Kodunuzda önemli satırlar:
```python
TURNSTILE_ENABLED = False  # ← Bunu True yapacağız
TURNSTILE_PIN = 18         # ← GPIO 18 kullanıyor
```

### Adım 1: Raspberry Pi'yi Açın ve Terminal'i Başlatın

### Adım 2: Proje Klasörüne Gidin
```bash
cd qr_turnstile_new
```

### Adım 3: Sanal Ortamı Aktifleştirin
```bash
source venv/bin/activate
```

### Adım 4: Kodu Düzenleyin
```bash
nano qr_turnstile_control.py
```

### Adım 5: Turnike Kontrolünü Aktifleştirin
Kodda şu satırı bulun:
```python
TURNSTILE_ENABLED = False
```

Şu şekilde değiştirin:
```python
TURNSTILE_ENABLED = True
```

### Adım 6: Dosyayı Kaydedin
- Ctrl + X tuşlarına basın
- Y tuşuna basın (Yes)
- Enter tuşuna basın

---

## 10. TEST AŞAMALARI

### Test 1: Yazılım Testi (Turnike Bağlı Değilken)

```bash
python qr_turnstile_control.py
```

**Beklenen Sonuç:**
- Program açılır
- QR kod okutma ekranı görünür
- Hata mesajı çıkmaz

### Test 2: GPIO Testi

**Terminal'de şu komutu çalıştırın:**
```bash
python3 -c "
import RPi.GPIO as GPIO
import time
GPIO.setmode(GPIO.BCM)
GPIO.setup(18, GPIO.OUT)
print('GPIO 18 HIGH')
GPIO.output(18, GPIO.HIGH)
time.sleep(2)
print('GPIO 18 LOW')
GPIO.output(18, GPIO.LOW)
GPIO.cleanup()
print('Test tamamlandı')
"
```

**Beklenen Sonuç:**
- Röle modülündeki LED yanıp söner
- "Tık" sesi duyulur

### Test 3: Multimetre ile Voltaj Testi (İsteğe Bağlı)

**Multimetre Ayarları:**
- DC Voltaj moduna alın
- 20V aralığını seçin

**Test Noktaları:**
1. **Güç Testi:** VCC ve GND arasında 5V olmalı
2. **Kontrol Testi:** IN ve GND arasında 0V/3.3V değişmeli
3. **Röle Çıkış Testi:** COM ve NO arasında açık/kapalı değişmeli

---

## 11. SORUN GİDERME

### Problem 1: Program Başlamıyor

**Hata:** "Permission denied" veya "GPIO already in use"

**Çözüm:**
```bash
sudo python qr_turnstile_control.py
```

### Problem 2: Röle Çalışmıyor

**Kontrol Listesi:**
- [ ] Kablolar doğru pinlere takılı mı?
- [ ] Kablo renkleri doğru mu?
- [ ] Röle modülü güç alıyor mu? (LED yanıyor mu?)
- [ ] TURNSTILE_ENABLED = True mu?

**Test Komutu:**
```bash
gpio readall
```

### Problem 3: Turnike Açılmıyor

**Kontrol Listesi:**
- [ ] Turnike gücü açık mı?
- [ ] Turnike kablolarını doğru buldunuz mu?
- [ ] COM ve NO pinleri kullanıyor musunuz?
- [ ] Turnike manuel olarak çalışıyor mu?

### Problem 4: QR Kod Okunmuyor

**Kontrol Listesi:**
- [ ] İnternet bağlantısı var mı?
- [ ] API erişilebilir mi? (https://api.gymkod.com)
- [ ] QR kod doğru formatında mı?

---

## 12. FARKLI TURNİKE MODELLERİ İÇİN ÖZEL AYARLAR

### Model 1: Standart Üçlü Turnike

**Özellikler:**
- 12V çalışma voltajı
- Dry contact kontrolü
- 2 kablo (+ ve -)

**Bağlantı:** Standart şema kullanın

### Model 2: Akıllı Turnike (Wiegand)

**Özellikler:**
- 12V/24V çalışma voltajı
- 4+ kablo
- Protokol desteği

**Bağlantı:** Sadece "Access Control" kablolarını kullanın

### Model 3: Motorlu Swing Gate

**Özellikler:**
- 24V çalışma voltajı
- Uzun açılma süresi
- Güvenlik sensörleri

**Kod Değişikliği Gerekli:**
```python
# 5 saniye yerine 10 saniye bekleyin
self.master.after(10000, self.close_turnstile)
```

---

## 13. BAKIM VE GÜVENLİK

### Günlük Kontroller:
- [ ] Raspberry Pi çalışıyor mu?
- [ ] İnternet bağlantısı var mı?
- [ ] Turnike manuel olarak çalışıyor mu?

### Haftalık Kontroller:
- [ ] Kablo bağlantıları sağlam mı?
- [ ] Röle modülü temiz mi?
- [ ] Log dosyalarında hata var mı?

### Güvenlik Önlemleri:
- Raspberry Pi'yi nemden koruyun
- Kablolara zarar vermeyin
- Turnike gücünü kesmeden çalışmayın
- Yedek röle modülü bulundurun

---

## 14. YEDEKLEME VE KURTARMA

### Kod Yedekleme:
```bash
cp qr_turnstile_control.py qr_turnstile_control_backup.py
```

### SD Kart Yedekleme:
- Win32DiskImager kullanın
- Tam sistem yedeği alın
- Aylık yedekleme yapın

### Acil Durum Planı:
1. Turnike manuel moduna alın
2. Raspberry Pi'yi yeniden başlatın
3. Yedek röle modülünü kullanın
4. Teknik destek arayın

---

## 15. İLETİŞİM VE DESTEK

### Teknik Destek İçin:
- Hata mesajlarını kaydedin
- Fotoğraf çekin
- Log dosyalarını saklayın

### Yaygın Hata Kodları:
- **GPIO Error:** Pin bağlantı hatası
- **Connection Error:** İnternet/API hatası
- **Permission Error:** Yetki hatası

---

## 16. EK BİLGİLER

### GPIO Alternatifleri:
GPIO 18 yerine kullanabileceğiniz pinler:
- GPIO 16 (Pin 36)
- GPIO 20 (Pin 38)
- GPIO 21 (Pin 40)

### Röle Modülü Alternatifleri:
- 2 kanallı röle (gelecekte çoklu turnike için)
- Solid state röle (daha sessiz)
- Optik izolasyonlu röle (daha güvenli)

### Gelişmiş Özellikler:
- Çoklu turnike desteği
- Uzaktan izleme
- Otomatik yeniden başlatma
- SMS/Email uyarıları

---

## 17. ADIM ADIM KURULUM ÖZETİ

### Hazırlık Aşaması:
1. **Malzeme Satın Alma:**
   - 1 kanallı röle modülü (15-30 TL)
   - 3 adet erkek-dişi jumper kablo (5-10 TL)
   - Multimetre (isteğe bağlı, 50-100 TL)

2. **Güvenlik Hazırlığı:**
   - Raspberry Pi'yi kapatın
   - Turnike gücünü kesin
   - Çalışma alanını temizleyin

### Bağlantı Aşaması:
3. **Raspberry Pi Bağlantıları:**
   - Kırmızı kablo: VCC → Pin 2 (5V)
   - Siyah kablo: GND → Pin 6 (GND)
   - Sarı kablo: IN → Pin 12 (GPIO 18)

4. **Turnike Bağlantıları:**
   - Turnike kontrol kutusunu bulun
   - "Access Control" bölümünü tespit edin
   - COM → + (Pozitif)
   - NO → - (Negatif)

### Test Aşaması:
5. **Yazılım Testi:**
   ```bash
   cd qr_turnstile_new
   source venv/bin/activate
   nano qr_turnstile_control.py
   # TURNSTILE_ENABLED = True yapın
   python qr_turnstile_control.py
   ```

6. **Donanım Testi:**
   - Röle LED'i yanıyor mu?
   - "Tık" sesi geliyor mu?
   - QR kod okutunca turnike açılıyor mu?

### Son Kontroller:
7. **Sistem Entegrasyonu:**
   - API bağlantısını test edin
   - Farklı QR kodlarla deneyin
   - Hata durumlarını kontrol edin

8. **Dokümantasyon:**
   - Bağlantı fotoğrafları çekin
   - Ayarları kaydedin
   - Yedek alın

---

## 18. SORU-CEVAP BÖLÜMÜ

### S: Röle modülü yerine başka bir şey kullanabilir miyim?
**C:** Hayır, röle modülü şarttır. Raspberry Pi'nin 3.3V çıkışı turnike motorunu çalıştıramaz.

### S: GPIO 18 yerine başka pin kullanabilir miyim?
**C:** Evet, GPIO 16, 20 veya 21 kullanabilirsiniz. Kodda TURNSTILE_PIN değerini değiştirin.

### S: Turnike çalışmıyor ama röle çalışıyor, ne yapmalıyım?
**C:** Turnike kablolarını kontrol edin. + ve - kablolarını yer değiştirmeyi deneyin.

### S: Birden fazla turnike bağlayabilir miyim?
**C:** Evet, her turnike için ayrı röle modülü ve GPIO pin kullanmanız gerekir.

### S: Sistem güvenli mi?
**C:** Evet, röle modülü optik izolasyon sağlar. Raspberry Pi ve turnike arasında elektriksel bağlantı yoktur.

### S: Uzaktan kontrol edebilir miyim?
**C:** Mevcut kod sadece yerel QR okuma destekler. Uzaktan kontrol için kod geliştirmesi gerekir.

---

Bu rehber, turnike kurulumunuzun başarılı olması için gerekli tüm bilgileri içermektedir. Her adımı dikkatlice takip edin ve güvenlik önlemlerini ihmal etmeyin.

**ÖNEMLİ NOT:** Bu rehberi yazdırdıktan sonra yanınızda bulundurun. Kurulum sırasında adım adım takip edebilirsiniz.
