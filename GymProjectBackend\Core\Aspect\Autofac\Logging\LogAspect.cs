﻿using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Core.Aspects.Autofac.Logging
{
    public class LogAspect : MethodInterception
    {
        private readonly ILogService _logService;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public LogAspect()
        {
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
            _httpContextAccessor = ServiceTool.ServiceProvider.GetService<IHttpContextAccessor>();
        }

        protected override void OnBefore(IInvocation invocation)
        {
            var logDetail = GetLogDetail(invocation);
            _logService.Info(JsonConvert.SerializeObject(logDetail));
        }

        protected override void OnException(IInvocation invocation, System.Exception e)
        {
            var logDetailWithException = new LogDetailWithException
            {
                MethodName = invocation.Method.Name,
                FullName = invocation.MethodInvocationTarget.DeclaringType.FullName,
                Parameters = GetLogParameters(invocation),
                ExceptionMessage = e.Message,
                ExceptionStackTrace = e.StackTrace,
                User = GetCurrentUser(),
                LogDate = DateTime.Now
            };

            _logService.Error(JsonConvert.SerializeObject(logDetailWithException));
        }

        private LogDetail GetLogDetail(IInvocation invocation)
        {
            var logParameters = GetLogParameters(invocation);

            var logDetail = new LogDetail
            {
                MethodName = invocation.Method.Name,
                FullName = invocation.MethodInvocationTarget.DeclaringType.FullName,
                Parameters = logParameters,
                User = GetCurrentUser(),
                LogDate = DateTime.Now
            };

            return logDetail;
        }

        private List<LogParameter> GetLogParameters(IInvocation invocation)
        {
            var logParameters = new List<LogParameter>();
            for (int i = 0; i < invocation.Arguments.Length; i++)
            {
                logParameters.Add(new LogParameter
                {
                    Name = invocation.GetConcreteMethod().GetParameters()[i].Name,
                    Value = invocation.Arguments[i],
                    Type = invocation.Arguments[i]?.GetType().Name
                });
            }

            return logParameters;
        }

        private string GetCurrentUser()
        {
            var username = _httpContextAccessor.HttpContext?.User?.Identity?.Name;
            return string.IsNullOrEmpty(username) ? "Anonymous" : username;
        }
    }
}