# RASPBERRY Pi TURNİKE KONTROL SİSTEMİ KURULUM REHBERİ
# Elektronik Bilgisi Olmayan Kullanıcılar İçin Detaylı Anlatım

## 1. SİSTEMİN GENEL ÇALIŞMA PRENSİBİ

Sisteminiz şu şekilde çalışıyor:
1. Raspberry Pi ekranında QR kod okutma programı çalışıyor
2. Kullanıcı QR kodunu okuttuğunda, sistem API'nize bağlanıyor
3. Üye bilgileri kontrol ediliyor
4. <PERSON><PERSON><PERSON> üyelik aktifse, GPIO 18 pini üzerinden röle modülüne sinyal gönderiliyor
5. Röle modülü turnikeyi tetikliyor ve turnike açılıyor

## 2. İHTİYAÇ LİSTESİ (SATIN ALMA LİSTESİ)

### A) Elektronik Malzemeler:
1. **1 Kanallı Röle Modülü** 
   - Marka: Arduino uyumlu herhangi bir marka
   - Özellik: 5V tetikleme, optik izolasyonlu
   - Fiyat: 15-25 TL arası
   - Nereden: Robotistan, Direnc.net, yerel elektronik mağazaları

2. **Jumper Kablolar (Erkek-Dişi)**
   - En az 10 adet
   - Renkli olması tercih edilir
   - Fiyat: 5-10 TL
   - Nereden: Aynı yerlerden

3. **Multimetre (İsteğe Bağlı)**
   - Test için kullanılacak
   - Basit dijital multimetre yeterli
   - Fiyat: 30-50 TL

### B) Turnike Bilgileri:
- Turnikenin marka ve modelini öğrenin
- Kontrol kutusunun yerini tespit edin
- Turnike teknik dökümanını isteyin (varsa)

## 3. RASPBERRY Pi YAZILIM KURULUMU

### Adım 1: Raspberry Pi'yi Güncelleyin
Terminal açın ve şu komutları sırayla yazın:

```bash
sudo apt update
sudo apt upgrade -y
```

### Adım 2: Gerekli Paketleri Kurun
```bash
sudo apt install python3-venv libzbar0 -y
```

### Adım 3: Proje Klasörünü Oluşturun
```bash
mkdir qr_turnstile_new
cd qr_turnstile_new
```

### Adım 4: Sanal Ortam Oluşturun
```bash
python3 -m venv venv
source venv/bin/activate
```

### Adım 5: Python Kütüphanelerini Kurun
```bash
pip install pillow requests RPi.GPIO pyautogui
```

### Adım 6: Python Kodunu Oluşturun
```bash
nano qr_turnstile_control.py
```

Açılan editöre verdiğiniz Python kodunu yapıştırın.
Kaydetmek için: Ctrl+X, sonra Y, sonra Enter

### Adım 7: Otomatik Başlatma Ayarı (İsteğe Bağlı)
```bash
nano ~/.bashrc
```

Dosyanın sonuna ekleyin:
```bash
cd /home/<USER>/qr_turnstile_new
source venv/bin/activate
python qr_turnstile_control.py
```

## 4. ELEKTRONİK BAĞLANTILAR (EN ÖNEMLİ BÖLÜM)

### UYARI: Bu işlemleri yapmadan önce mutlaka güçleri kesin!

### Adım 1: Raspberry Pi'yi Kapatın
```bash
sudo shutdown -h now
```
Güç LED'i sönene kadar bekleyin, sonra güç kablosunu çekin.

### Adım 2: Röle Modülü Bağlantıları

**Raspberry Pi Pin Haritası (40 pinli):**
```
Pin 1  [3.3V]    [5V]     Pin 2
Pin 3  [GPIO2]   [5V]     Pin 4  
Pin 5  [GPIO3]   [GND]    Pin 6
Pin 7  [GPIO4]   [GPIO14] Pin 8
Pin 9  [GND]     [GPIO15] Pin 10
Pin 11 [GPIO17]  [GPIO18] Pin 12  ← Bu pin kullanılacak
Pin 13 [GPIO27]  [GND]    Pin 14
...
```

**Bağlantı Şeması:**
```
Röle Modülü Pini    →    Raspberry Pi Pini    →    Kablo Rengi (Önerilen)
VCC                 →    Pin 2 (5V)           →    Kırmızı
GND                 →    Pin 6 (GND)          →    Siyah  
IN                  →    Pin 12 (GPIO 18)     →    Sarı/Yeşil
```

### Adım 3: Bağlantıları Yapın

1. **Kırmızı kabloyu** röle modülünün **VCC** pinine takın
2. **Kırmızı kablonun** diğer ucunu Raspberry Pi'nin **Pin 2** (5V) sine takın
3. **Siyah kabloyu** röle modülünün **GND** pinine takın  
4. **Siyah kablonun** diğer ucunu Raspberry Pi'nin **Pin 6** (GND) sine takın
5. **Sarı kabloyu** röle modülünün **IN** pinine takın
6. **Sarı kablonun** diğer ucunu Raspberry Pi'nin **Pin 12** (GPIO 18) sine takın

### Adım 4: Turnike Bağlantıları

**Turnike Kontrol Kutusunu Bulun:**
- Genellikle turnikenin yanında veya altında metal bir kutu vardır
- "Control Box" veya "Kontrol Ünitesi" yazabilir

**Kontrol Kutusunda Arayacağınız Bağlantılar:**
- "ACCESS CONTROL" yazan bölüm
- "DRY CONTACT" yazan terminaller  
- "OPEN" veya "UNLOCK" yazan bağlantılar
- "+" ve "-" işaretli terminaller

**Röle Modülünden Turnikeye Bağlantı:**
```
Röle Modülü         →    Turnike Kontrol Kutusu
COM (Ortak)         →    ACCESS+ veya OPEN+
NO (Normally Open)  →    ACCESS- veya OPEN-
```

## 5. KODDA YAPILACAK DEĞİŞİKLİKLER

Kodunuzda turnike kontrolü şu anda devre dışı. Aktifleştirmek için:

### Değiştirilecek Satır:
```python
TURNSTILE_ENABLED = False  # Bu satırı bulun
```

### Şu şekilde değiştirin:
```python
TURNSTILE_ENABLED = True   # False yerine True yazın
```

## 6. TEST AŞAMALARI

### Test 1: Yazılım Testi
1. Raspberry Pi'yi başlatın
2. Terminal açın ve şu komutları çalıştırın:
```bash
cd qr_turnstile_new
source venv/bin/activate
python qr_turnstile_control.py
```
3. Program açılmalı ve QR kod beklemelidir

### Test 2: Röle Testi
1. Geçerli bir QR kod okutun
2. Röle modülündeki LED yanmalı
3. Röleden "tık" sesi gelmeli
4. 5 saniye sonra LED sönmeli

### Test 3: Turnike Testi  
1. Röle çalıştığında turnike açılmalı
2. 5 saniye sonra turnike kapanmalı

## 7. SORUN GİDERME

### Problem 1: Program Çalışmıyor
**Belirtiler:** Python hatası alıyorsunuz
**Çözüm:** 
```bash
pip install --upgrade pillow requests RPi.GPIO pyautogui
```

### Problem 2: Röle LED'i Yanmıyor
**Belirtiler:** QR kod okuttuğunuzda röle LED'i yanmıyor
**Kontrol Edilecekler:**
- Kırmızı kablo Pin 2'ye takılı mı?
- Siyah kablo Pin 6'ya takılı mı?
- Kablolar sıkı mı?

### Problem 3: Röle Çalışıyor Ama Turnike Açılmıyor
**Belirtiler:** Röle "tık" sesi çıkarıyor ama turnike hareketsiz
**Kontrol Edilecekler:**
- Turnike güç kablosu takılı mı?
- Röle COM ve NO pinleri doğru yere bağlı mı?
- Turnike kontrol kutusundaki bağlantılar doğru mu?

### Problem 4: Turnike Sürekli Açık Kalıyor
**Belirtiler:** Turnike kapanmıyor
**Çözüm:** 
- Röle kablolarını COM ve NC pinlerine bağlayın (NO yerine NC)

## 8. GÜVENLİK ÖNLEMLERİ

### Kritik Kurallar:
1. **Asla canlı sistemde çalışmayın** - Önce güçleri kesin
2. **Kablo renklerini karıştırmayın** - Yanlış bağlantı cihazları yakabilir
3. **5V ile 3.3V'u karıştırmayın** - Raspberry Pi yanabilir
4. **Çift kontrol yapın** - Bağlantıları iki kez kontrol edin
5. **Test aşamalı yapın** - Önce yazılım, sonra röle, en son turnike

## 9. FARKLI TURNİKE MODELLERİ İÇİN AYARLAMALAR

### Tripod (Üçlü) Turnike:
- En yaygın tip
- Genellikle 12V çalışır
- Dry contact bağlantısı vardır
- Yukarıdaki bağlantı şeması çalışır

### Full Height (Boy) Turnike:
- 24V çalışabilir
- Daha güçlü röle gerekebilir
- Kontrol kutusu daha karmaşık olabilir

### Swing Gate:
- Çift yönlü çalışır
- İki ayrı kontrol girişi olabilir
- Engelli erişimi için kullanılır

## 10. BAKIM VE İZLEME

### Günlük Kontroller:
- Raspberry Pi çalışıyor mu?
- Program otomatik başlıyor mu?
- QR kod okuma hızı normal mi?

### Haftalık Kontroller:
- Röle bağlantıları sıkı mı?
- Turnike mekanik aksamı çalışıyor mu?
- Sistem loglarında hata var mı?

### Aylık Kontroller:
- Raspberry Pi güncellemesi
- Python kütüphaneleri güncellemesi
- Yedekleme

## 11. YEDEKLEME VE KURTARMA

### Kod Yedeği:
```bash
cp qr_turnstile_control.py qr_turnstile_control_backup.py
```

### Sistem Yedeği:
- SD kartın imajını alın
- Ayar dosyalarını yedekleyin

## 12. DESTEK VE İLETİŞİM

### Hata Durumunda:
1. Hata mesajını tam olarak kaydedin
2. Hangi adımda hata aldığınızı belirtin
3. Turnike marka/modelini belirtin
4. Fotoğraf çekin (bağlantılar, hata ekranı)

### Teknik Destek İçin Gerekli Bilgiler:
- Raspberry Pi modeli
- Turnike marka/modeli
- Röle modülü markası
- Hata mesajı
- Bağlantı fotoğrafları

## 13. KODUNUZUN ANALİZİ VE ÖNEMLİ NOKTALAR

### Kodunuzda Dikkat Edilecek Noktalar:

1. **TURNSTILE_ENABLED = False**
   - Bu satır turnike kontrolünü devre dışı bırakıyor
   - Bağlantıları yaptıktan sonra True yapın

2. **GPIO 18 Pin Kullanımı:**
   - Kodunuz GPIO 18 pinini kullanıyor
   - Bu Pin 12'ye denk geliyor
   - Değiştirmek isterseniz TURNSTILE_PIN değerini değiştirin

3. **5 Saniye Açık Kalma:**
   - Turnike 5 saniye açık kalıyor
   - Bu süreyi değiştirmek için open_turnstile() fonksiyonundaki 5000 değerini değiştirin

4. **API Bağlantısı:**
   - Kodunuz https://api.gymkod.com adresine bağlanıyor
   - İnternet bağlantısı gerekli
   - API çalışmazsa turnike de çalışmaz

## 14. ADIM ADIM KURULUM ÖZETİ

### Hazırlık Aşaması:
1. Röle modülü ve kablo satın alın
2. Turnike kontrol kutusunu bulun
3. Raspberry Pi'yi güncelleyin

### Yazılım Kurulumu:
1. Python ortamını hazırlayın
2. Kodu yerleştirin
3. Test edin (turnike bağlantısı olmadan)

### Donanım Kurulumu:
1. Güçleri kesin
2. Röle modülünü Raspberry Pi'ye bağlayın
3. Röle modülünü turnikeye bağlayın
4. Güçleri açın

### Test Aşaması:
1. Yazılım testini yapın
2. Röle testini yapın
3. Turnike testini yapın

## 15. SONUÇ VE ÖNERİLER

Bu rehber size adım adım turnike kurulumunu anlatmıştır. En önemli noktalar:

1. **Güvenlik öncelikli** - Asla canlı sistemde çalışmayın
2. **Adım adım ilerleyin** - Her aşamayı test edin
3. **Dokümantasyon tutun** - Yaptığınız değişiklikleri kaydedin
4. **Yedek alın** - Çalışan sistemi yedekleyin
5. **Sabırlı olun** - İlk kurulumda sorunlar normal

### İlk Kurulum İçin Öneriler:
- Önce masada test edin
- Sonra turnikeye bağlayın
- Her adımı fotoğraflayın
- Çalışan ayarları kaydedin

### Yaygın Hatalar:
- Güç kesilmeden çalışmak
- Kablo renklerini karıştırmak
- Test aşamalarını atlamak
- Yedek almamak

Başarılar! Bu rehberi takip ederek sisteminizi güvenle kurabilirsiniz.

## 16. EK BİLGİLER

### Raspberry Pi GPIO Pin Haritası (Detaylı):
```
     3V3  (1) (2)  5V
   GPIO2  (3) (4)  5V
   GPIO3  (5) (6)  GND
   GPIO4  (7) (8)  GPIO14
     GND  (9) (10) GPIO15
  GPIO17 (11) (12) GPIO18  ← KULLANILAN PIN
  GPIO27 (13) (14) GND
  GPIO22 (15) (16) GPIO23
     3V3 (17) (18) GPIO24
  GPIO10 (19) (20) GND
   GPIO9 (21) (22) GPIO25
  GPIO11 (23) (24) GPIO8
     GND (25) (26) GPIO7
   GPIO0 (27) (28) GPIO1
   GPIO5 (29) (30) GND
   GPIO6 (31) (32) GPIO12
  GPIO13 (33) (34) GND
  GPIO19 (35) (36) GPIO16
  GPIO26 (37) (38) GPIO20
     GND (39) (40) GPIO21
```

### Röle Modülü Çeşitleri:
1. **Aktif Yüksek (Active High):** GPIO HIGH olduğunda röle açılır
2. **Aktif Düşük (Active Low):** GPIO LOW olduğunda röle açılır
3. **Optik İzolasyonlu:** Daha güvenli
4. **Optik İzolasyonsuz:** Daha ucuz ama riskli

### Turnike Kontrol Sinyalleri:
1. **Dry Contact:** Sadece anahtar görevi
2. **Wet Contact:** Voltaj sinyali gerekir
3. **Pulse:** Kısa süreli sinyal
4. **Level:** Sürekli sinyal

Bu bilgiler ile sisteminizi başarıyla kurabilir ve işletebilirsiniz!
