﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicensePackageManager : ILicensePackageService
    {
        private readonly ILicensePackageDal _licensePackageDal;

        public LicensePackageManager(ILicensePackageDal licensePackageDal)
        {
            _licensePackageDal = licensePackageDal;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        //[DebugCacheRemoveAspect("Business.Concrete.LicensePackageManager.GetAll")]
        public IResult Add(LicensePackage licensePackage)
        {
            licensePackage.CreationDate = DateTime.Now;
            licensePackage.IsActive = true;
            _licensePackageDal.Add(licensePackage);
            return new SuccessResult("Lisans paketi başarıyla eklendi");
        }

        [LogAspect]
        [SecuredOperation("owner")]
        //[DebugCacheRemoveAspect("Business.Concrete.LicensePackageManager.GetAll")]
        public IResult Delete(int id)
        {
            _licensePackageDal.Delete(id);
            return new SuccessResult("Lisans paketi başarıyla silindi");
        }

        [PerformanceAspect(3)]
        //[CacheAspect(duration: 60)]
        [SecuredOperation("owner")]
        public IDataResult<List<LicensePackage>> GetAll()
        {
            return new SuccessDataResult<List<LicensePackage>>(_licensePackageDal.GetAll(lp => lp.IsActive == true));
        }

        [PerformanceAspect(3)]
        //[CacheAspect(duration: 60)]
        [SecuredOperation("owner")]
        public IDataResult<LicensePackage> GetById(int id)
        {
            return new SuccessDataResult<LicensePackage>(_licensePackageDal.Get(lp => lp.LicensePackageID == id));
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        //[DebugCacheRemoveAspect("Business.Concrete.LicensePackageManager.GetAll")]
        public IResult Update(LicensePackage licensePackage)
        {
            licensePackage.UpdatedDate = DateTime.Now;
            _licensePackageDal.Update(licensePackage);
            return new SuccessResult("Lisans paketi başarıyla güncellendi");
        }
    }
}