﻿using Autofac.Core;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Microsoft;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.DependencyResolvers
{
    public class CoreModule : ICoreModule
    {
        public void Load(IServiceCollection serviceCollection)
        {
            
            serviceCollection.AddMemoryCache();
            serviceCollection.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
            serviceCollection.AddScoped<Core.Utilities.Security.CompanyContext.ICompanyContext, Core.Utilities.Security.CompanyContext.CompanyContext>();
            serviceCollection.AddSingleton<ICacheManager, DebugCacheManager>();
            serviceCollection.AddSingleton<Stopwatch>();
            serviceCollection.AddSingleton<FileLoggerService>();
            serviceCollection.AddSingleton<PerformanceLoggerService>();
            serviceCollection.AddSingleton<ILogService, FileLoggerService>();
        }
    }
}
