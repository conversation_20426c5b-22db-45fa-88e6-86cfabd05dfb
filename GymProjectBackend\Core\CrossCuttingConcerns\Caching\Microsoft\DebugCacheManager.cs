using Core.Utilities.IoC;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.RegularExpressions;

namespace Core.CrossCuttingConcerns.Caching.Microsoft
{
    public class DebugCacheManager : ICacheManager
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ConcurrentDictionary<string, string> _allKeys;

        public DebugCacheManager()
        {
            _memoryCache = ServiceTool.ServiceProvider.GetService<IMemoryCache>();
            _allKeys = new ConcurrentDictionary<string, string>();
        }

        public void Add(string key, object value, int duration)
        {
            Debug.WriteLine($"Cache Key Added: {key}");
            _memoryCache.Set(key, value, TimeSpan.FromMinutes(duration));
            
            // Anahtarı şirket ID'si ile birlikte sakla
            string companyPrefix = ExtractCompanyPrefix(key);
            _allKeys.TryAdd(key, companyPrefix);
            
            // Tüm cache anahtarlarını yazdır
            Debug.WriteLine("All Cache Keys:");
            foreach (var cacheKey in _allKeys.Keys)
            {
                Debug.WriteLine($"  - {cacheKey} (Company: {_allKeys[cacheKey]})");
            }
        }

        public T Get<T>(string key)
        {
            Debug.WriteLine($"Cache Get<T>: {key}");
            return _memoryCache.Get<T>(key);
        }

        public object Get(string key)
        {
            Debug.WriteLine($"Cache Get: {key}");
            return _memoryCache.Get(key);
        }

        public bool IsAdd(string key)
        {
            var exists = _memoryCache.TryGetValue(key, out _);
            Debug.WriteLine($"Cache IsAdd: {key}, Exists: {exists}");
            return exists;
        }

        public void Remove(string key)
        {
            Debug.WriteLine($"Cache Remove: {key}");
            _memoryCache.Remove(key);
            _allKeys.TryRemove(key, out _);
        }

        public void RemoveByPattern(string pattern)
        {
            Debug.WriteLine($"Cache RemoveByPattern: {pattern}");
            
            // Eğer pattern bir şirket ID'si içeriyorsa (örn: Company_123_...)
            if (pattern.StartsWith("Company_"))
            {
                // Şirket ID'sini çıkar (örn: Company_123)
                string companyPrefix = ExtractCompanyPrefix(pattern);
                Debug.WriteLine($"Extracted company prefix: {companyPrefix}");
                
                // Bu şirkete ait tüm anahtarları bul
                var keysToRemove = _allKeys
                    .Where(pair => pair.Value == companyPrefix && IsPatternMatch(pair.Key, pattern))
                    .Select(pair => pair.Key)
                    .ToList();
                
                Debug.WriteLine($"Pattern: {pattern}, Matching Keys Count: {keysToRemove.Count}");
                foreach (var key in keysToRemove)
                {
                    Debug.WriteLine($"  - Removing Key: {key}");
                    Remove(key);
                }
                
                // Eğer hiç anahtar bulunamadıysa, daha genel bir yaklaşım dene
                if (keysToRemove.Count == 0)
                {
                    Debug.WriteLine("No keys matched with specific pattern, trying more general approach");
                    
                    // Metot adını çıkar (eğer varsa)
                    string methodName = "";
                    if (pattern.Count(c => c == '_') >= 2)
                    {
                        int secondUnderscore = pattern.IndexOf('_', pattern.IndexOf('_') + 1);
                        methodName = pattern.Substring(secondUnderscore + 1);
                    }
                    
                    // Sadece şirket önekine göre eşleştir
                    keysToRemove = _allKeys
                        .Where(pair => pair.Value == companyPrefix && 
                                (string.IsNullOrEmpty(methodName) || pair.Key.Contains(methodName)))
                        .Select(pair => pair.Key)
                        .ToList();
                    
                    Debug.WriteLine($"General approach - Pattern: {companyPrefix}, Method: {methodName}, Matching Keys Count: {keysToRemove.Count}");
                    foreach (var key in keysToRemove)
                    {
                        Debug.WriteLine($"  - Removing Key: {key}");
                        Remove(key);
                    }
                }
            }
            else
            {
                // Eğer şirket ID'si yoksa, normal regex eşleştirmesi yap
                try
                {
                    var regex = new Regex(pattern, RegexOptions.Singleline | RegexOptions.Compiled | RegexOptions.IgnoreCase);
                    var keysToRemove = _allKeys.Keys.Where(key => regex.IsMatch(key)).ToList();
                    
                    Debug.WriteLine($"Pattern: {pattern}, Matching Keys Count: {keysToRemove.Count}");
                    foreach (var key in keysToRemove)
                    {
                        Debug.WriteLine($"  - Removing Key: {key}");
                        Remove(key);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error in regex pattern matching: {ex.Message}");
                    
                    // Regex hatası durumunda basit string içerme kontrolü yap
                    var keysToRemove = _allKeys.Keys.Where(key => key.Contains(pattern)).ToList();
                    
                    Debug.WriteLine($"Simple contains - Pattern: {pattern}, Matching Keys Count: {keysToRemove.Count}");
                    foreach (var key in keysToRemove)
                    {
                        Debug.WriteLine($"  - Removing Key: {key}");
                        Remove(key);
                    }
                }
            }
            
            // Kalan cache anahtarlarını yazdır
            Debug.WriteLine("Remaining Cache Keys:");
            foreach (var cacheKey in _allKeys.Keys)
            {
                Debug.WriteLine($"  - {cacheKey} (Company: {_allKeys[cacheKey]})");
            }
        }
        
        // Anahtar deseninden şirket önekini çıkarır (örn: "Company_123_Method" -> "Company_123")
        private string ExtractCompanyPrefix(string key)
        {
            if (string.IsNullOrEmpty(key))
                return string.Empty;
                
            if (!key.StartsWith("Company_"))
                return string.Empty;
                
            // İlk alt çizgiden sonraki ikinci alt çizgiyi bul
            int firstUnderscore = key.IndexOf('_');
            if (firstUnderscore < 0) return string.Empty;
            
            int secondUnderscore = key.IndexOf('_', firstUnderscore + 1);
            if (secondUnderscore < 0) return key; // İkinci alt çizgi yoksa tüm anahtarı döndür
            
            return key.Substring(0, secondUnderscore);
        }
        
        // Bir anahtarın belirli bir desene uyup uymadığını kontrol eder
        private bool IsPatternMatch(string key, string pattern)
        {
            // Eğer key veya pattern null ise eşleşme yok
            if (string.IsNullOrEmpty(key) || string.IsNullOrEmpty(pattern))
                return false;
                
            // Eğer pattern bir metot adı içeriyorsa (örn: Company_123_MethodName)
            if (pattern.Count(c => c == '_') >= 2)
            {
                // Metot adını çıkar
                int secondUnderscore = pattern.IndexOf('_', pattern.IndexOf('_') + 1);
                string methodPattern = pattern.Substring(secondUnderscore + 1);
                
                // Anahtardan metot adını çıkar
                int keySecondUnderscore = key.IndexOf('_', key.IndexOf('_') + 1);
                if (keySecondUnderscore < 0) return false;
                
                string keyMethod = key.Substring(keySecondUnderscore + 1);
                
                // Metot adı eşleşiyor mu kontrol et
                // Parantez içindeki argümanları dikkate almadan eşleştir
                int openParenIndex = keyMethod.IndexOf('(');
                if (openParenIndex >= 0)
                {
                    keyMethod = keyMethod.Substring(0, openParenIndex);
                }
                
                // Tam metot adı eşleşmesi için
                if (methodPattern.Contains("."))
                {
                    return keyMethod.Equals(methodPattern);
                }
                
                // Sadece metot adı verilmişse (sınıf adı olmadan)
                int lastDotIndex = keyMethod.LastIndexOf('.');
                if (lastDotIndex >= 0)
                {
                    string onlyMethodName = keyMethod.Substring(lastDotIndex + 1);
                    return onlyMethodName.Equals(methodPattern);
                }
                
                return keyMethod.Equals(methodPattern);
            }
            
            // Eğer sadece şirket ID'si varsa, her zaman eşleşir
            return true;
        }
    }
}
