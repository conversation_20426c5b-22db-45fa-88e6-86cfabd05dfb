﻿using System;
using System.IO;

namespace Core.CrossCuttingConcerns.Logging.FileLogger
{
    public class FileLoggerService : ILogService
    {
        private readonly string _logDirectory;
        private readonly string _regularLogDirectory;
        private readonly object _lock = new object();

        public FileLoggerService()
        {
            // Ana log klasörü
            _logDirectory = @"C:\GymProjectLogs";

            // Normal loglar için klasör
            _regularLogDirectory = Path.Combine(_logDirectory, "Logs");

            // Klasörleri oluştur
            Directory.CreateDirectory(_logDirectory);
            Directory.CreateDirectory(_regularLogDirectory);
        }

        private void Log(string level, string message)
        {
            var logFilePath = Path.Combine(_regularLogDirectory, $"log_{DateTime.Now:dd-MM-yyyy}.txt");
            var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} [{level}] {message}";

            lock (_lock)
            {
                try
                {
                    File.AppendAllText(logFilePath, logMessage + Environment.NewLine + Environment.NewLine);
                }
                catch
                {
                }
            }
        }

        public void Info(string message, bool isPerformanceLog = false)
        {
            Log("INFO", message);
        }

        public void Debug(string message)
        {
            Log("DEBUG", message);
        }

        public void Warn(string message)
        {
            Log("WARN", message);
        }

        public void Error(string message)
        {
            Log("ERROR", message);
        }

        public void Fatal(string message)
        {
            Log("FATAL", message);
        }

        public void LogPerformance(string message)
        {
            Log("PERFORMANCE", message);
        }
    }
}