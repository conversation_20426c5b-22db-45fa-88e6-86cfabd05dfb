﻿using Business.Abstract;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class TownController : ControllerBase
    {
        ITownService _townService;

        public TownController(ITownService townService)
        {
            _townService = townService;
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _townService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpGet("getbycityid")]

        public IActionResult GetByCityId(int id)
        {
            var result = _townService.GetByCityId(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
