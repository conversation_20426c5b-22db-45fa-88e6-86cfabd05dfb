﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class LicenseTransactionManager : ILicenseTransactionService
    {
        private readonly ILicenseTransactionDal _licenseTransactionDal;

        public LicenseTransactionManager(ILicenseTransactionDal licenseTransactionDal)
        {
            _licenseTransactionDal = licenseTransactionDal;
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [CacheRemoveAspect("ILicenseTransactionService.Get")]
        public IResult Add(LicenseTransaction licenseTransaction)
        {
            licenseTransaction.CreationDate = DateTime.Now;
            licenseTransaction.IsActive = true;
            _licenseTransactionDal.Add(licenseTransaction);
            return new SuccessResult("Lisans işlemi başarıyla eklendi");
        }

        [PerformanceAspect(3)]
        //[CacheAspect(duration: 60)]
        [SecuredOperation("owner")]
        public IDataResult<List<LicenseTransaction>> GetAll()
        {
            return new SuccessDataResult<List<LicenseTransaction>>(_licenseTransactionDal.GetAll(lt => lt.IsActive));
        }

        [PerformanceAspect(3)]
        //[CacheAspect(duration: 60)]
        [SecuredOperation("owner")]
        public IDataResult<List<LicenseTransaction>> GetByUserId(int userId)
        {
            return new SuccessDataResult<List<LicenseTransaction>>(
                _licenseTransactionDal.GetAll(lt => lt.UserID == userId && lt.IsActive));
        }
    }
}