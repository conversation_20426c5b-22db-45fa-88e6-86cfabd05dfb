﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;

namespace Business.Concrete
{
    public class UserOperationClaimManager : IUserOperationClaimService
    {
        private readonly IUserOperationClaimDal _userOperationClaimDal;
        private readonly IUserDeviceService _userDeviceService;

        public UserOperationClaimManager(
            IUserOperationClaimDal userOperationClaimDal,
            IUserDeviceService userDeviceService)
        {
            _userOperationClaimDal = userOperationClaimDal;
            _userDeviceService = userDeviceService;
        }

        [SecuredOperation("owner")]
        public IDataResult<List<UserOperationClaimDto>> GetUserOperationClaimDetails()
        {
            return new SuccessDataResult<List<UserOperationClaimDto>>(_userOperationClaimDal.GetUserOperationClaimDetails());
        }

        [LogAspect]
        [SecuredOperation("owner")]
        public IResult Add(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Add(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimAdded);
        }

        [LogAspect]
        [SecuredOperation("owner")]
        public IResult Delete(int id)
        {
            var claim = _userOperationClaimDal.Get(x => x.UserOperationClaimId == id);
            if (claim != null)
            {
                _userOperationClaimDal.HardDelete(id);
                InvalidateUserClaims(claim.UserId);
            }
            return new SuccessResult(Messages.UserOperationClaimDeleted);
        }

        [SecuredOperation("owner")]
        public IDataResult<List<UserOperationClaim>> GetAll()
        {
            return new SuccessDataResult<List<UserOperationClaim>>(_userOperationClaimDal.GetAll(u=>u.IsActive==true), Messages.UserOperationClaimsListed);
        }

        [SecuredOperation("owner")]
        public IDataResult<UserOperationClaim> GetById(int id)
        {
            return new SuccessDataResult<UserOperationClaim>(_userOperationClaimDal.Get(u => u.UserOperationClaimId == id));
        }

        [LogAspect]
        [SecuredOperation("owner")]
        public IResult Update(UserOperationClaim userOperationClaim)
        {
            _userOperationClaimDal.Update(userOperationClaim);
            InvalidateUserClaims(userOperationClaim.UserId);
            return new SuccessResult(Messages.UserOperationClaimUpdated);
        }

        public IResult InvalidateUserClaims(int userId)
        {
            var userDevices = _userDeviceService.GetActiveDevicesByUserId(userId);
            if (userDevices.Success)
            {
                foreach (var device in userDevices.Data)
                {
                    // Force token refresh by expiring the current refresh token
                    device.RefreshTokenExpiration = DateTime.Now;
                    _userDeviceService.Update(device);
                }
            }
            return new SuccessResult();
        }
    }
}
