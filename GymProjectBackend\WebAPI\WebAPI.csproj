<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Autofac" Version="8.0.0" />
    <PackageReference Include="Autofac.Extensions.DependencyInjection" Version="9.0.0" />
    <PackageReference Include="Autofac.Extras.DynamicProxy" Version="7.1.0" />
    <PackageReference Include="Azure.Identity" Version="1.13.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="System.Formats.Asn1" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Business\Business.csproj" />
    <ProjectReference Include="..\Core\Core.csproj" />
    <ProjectReference Include="..\DataAccess\DataAccess.csproj" />
    <ProjectReference Include="..\Entities\Entities.csproj" />
  </ItemGroup>

</Project>
