﻿using FluentValidation;
using FluentValidation.Results;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Net;
using Core.CrossCuttingConcerns.Logging;
using Core.Utilities.IoC;
using Microsoft.Extensions.DependencyInjection;
using Core.Extentions;

namespace Core.Extensions
{
    public class ExceptionMiddleware
    {
        private RequestDelegate _next;
        private readonly ILogService _logService;

        public ExceptionMiddleware(RequestDelegate next)
        {
            _next = next;
            _logService = ServiceTool.ServiceProvider.GetService<ILogService>();
        }

        public async Task InvokeAsync(HttpContext httpContext)
        {
            try
            {
                await _next(httpContext);
            }
            catch (Exception e)
            {
                await HandleExceptionAsync(httpContext, e);
            }
        }

        private async Task HandleExceptionAsync(HttpContext httpContext, Exception e)
        {
            httpContext.Response.ContentType = "application/json";
            httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

            string message = "Internal Server Error";
            IEnumerable<ValidationFailure> errors;

            if (e.GetType() == typeof(ValidationException))
            {
                message = e.Message;
                errors = ((ValidationException)e).Errors;
                httpContext.Response.StatusCode = 400;

                // Log validation errors
                _logService.Warn($"Validation Error: {JsonConvert.SerializeObject(errors)}");

                await httpContext.Response.WriteAsync(new ValidationErrorDetails
                {
                    StatusCode = 400,
                    Message = message,
                    Errors = errors
                }.ToString());
                return;
            }

            // Log the exception
            _logService.Error($"Exception: {e.Message}\nStackTrace: {e.StackTrace}");

            await httpContext.Response.WriteAsync(new ErrorDetails
            {
                StatusCode = httpContext.Response.StatusCode,
                Message = e.Message
            }.ToString());
        }
    }
}