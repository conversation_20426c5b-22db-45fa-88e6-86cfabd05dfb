﻿﻿using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Caching
{
    public class CacheRemoveAspect : MethodInterception
    {
        private string _pattern;
        private ICacheManager _cacheManager;
        private ICompanyContext _companyContext;

        public CacheRemoveAspect(string pattern)
        {
            _pattern = pattern;
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        protected override void OnSuccess(IInvocation invocation)
        {
            // Şirket ID'sini al
            int companyId = -1;
            if (_companyContext != null)
            {
                companyId = _companyContext.GetCompanyId();
            }

            // Şirket ID'sine özel pattern oluştur
            string companyPattern = $"Company_{companyId}_{_pattern}";
            
            _cacheManager.RemoveByPattern(companyPattern);
        }
    }
}
