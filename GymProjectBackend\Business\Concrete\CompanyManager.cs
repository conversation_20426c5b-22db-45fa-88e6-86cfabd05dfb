﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyManager : ICompanyService
    {
        ICompanyDal _companyDal;

        public CompanyManager(ICompanyDal companyDal)
        {
            _companyDal = companyDal;
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyValidator))]
        public IResult Add(Company company)
        {
            _companyDal.Add(company);
            return new SuccessResult(Messages.CompanyAdded);
        }
        [LogAspect]
        [SecuredOperation("owner")]
        public IResult Delete(int id)
        {
            _companyDal.Delete(id);
            return new SuccessResult(Messages.CompanyDeleted);
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        public IDataResult<List<ActiveCompanyDetailDto>> GetActiveCompanies()
        {
            return new SuccessDataResult<List<ActiveCompanyDetailDto>>(_companyDal.GetActiveCompanies());
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        public IDataResult<List<Company>> GetAll()
        {
            return new SuccessDataResult<List<Company>>(_companyDal.GetAll());
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner")]
        [ValidationAspect(typeof(CompanyValidator))]
        public IResult Update(Company company)
        {
            _companyDal.Update(company);
            return new SuccessResult(Messages.CompanyUpdated);
        }
    }
}
