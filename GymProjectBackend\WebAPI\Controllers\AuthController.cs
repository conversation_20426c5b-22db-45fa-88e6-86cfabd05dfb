﻿using Business.Abstract;
using Core.Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Microsoft.AspNetCore.RateLimiting; // Rate Limiting için eklendi

[Route("api/[controller]")]
[ApiController]
public class AuthController : Controller
{
    private IAuthService _authService;

    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    [HttpPost("register")]
    [EnableRateLimiting("IPRateLimit")] // Rate limiting eklendi
    public ActionResult Register([FromBody] RegisterRequestModel request)
    {
        var userExists = _authService.UserExists(request.RegisterDto.Email);
        if (!userExists.Success)
        {
            return BadRequest(new { success = false, message = userExists.Message });
        }

        var registerResult = _authService.Register(request.RegisterDto, request.RegisterDto.Password);
        if (!registerResult.Success)
        {
            return BadRequest(new { success = false, message = registerResult.Message });
        }

        var result = _authService.CreateAccessToken(registerResult.Data, request.DeviceInfo);
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("login")]
    [EnableRateLimiting("IPRateLimit")] // Rate limiting eklendi
    public ActionResult Login([FromBody] LoginRequestModel request)
    {
        var userToLogin = _authService.Login(request.LoginDto, request.DeviceInfo);
        if (!userToLogin.Success)
        {
            return BadRequest(new { success = false, message = userToLogin.Message });
        }

        var result = _authService.CreateAccessToken(userToLogin.Data, request.DeviceInfo);
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("refresh-token")]
    public ActionResult RefreshToken([FromBody] RefreshTokenRequest request)
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result = _authService.CreateAccessTokenWithRefreshToken(request.RefreshToken, ipAddress, request.DeviceInfo);

        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("logout")]
    public ActionResult Logout()
    {
        var refreshToken = Request.Headers["X-Refresh-Token"].ToString();
        if (string.IsNullOrEmpty(refreshToken))
        {
            return BadRequest(new { success = false, message = "Refresh token not provided" });
        }

        var result = _authService.RevokeRefreshToken(refreshToken);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("revoke-device")]
    public ActionResult RevokeDevice(int deviceId)
    {
        var result = _authService.RevokeDevice(deviceId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("revoke-all-devices")]
    public ActionResult RevokeAllDevices()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.RevokeAllDevices(userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpGet("devices")]
    public ActionResult GetUserDevices()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.GetUserDevices(userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("change-company")]
    public ActionResult ChangeCompany([FromBody] ChangeCompanyRequest request)
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.ChangeCompany(userId, request.CompanyId, request.DeviceInfo);
        
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    public class RegisterRequestModel
    {
        public UserForRegisterDto RegisterDto { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class LoginRequestModel
    {
        public UserForLoginDto LoginDto { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class RefreshTokenRequest
    {
        [Required]
        public string RefreshToken { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class ChangeCompanyRequest
    {
        [Required]
        public int CompanyId { get; set; }
        public string DeviceInfo { get; set; }
    }
}