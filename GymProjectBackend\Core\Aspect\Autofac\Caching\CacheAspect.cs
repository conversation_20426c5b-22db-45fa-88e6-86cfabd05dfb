﻿﻿using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.Aspects.Autofac.Caching
{
    public class CacheAspect : MethodInterception
    {
        private int _duration;
        private ICacheManager _cacheManager;
        private ICompanyContext _companyContext;

        public CacheAspect(int duration = 60)
        {
            _duration = duration;
            _cacheManager = ServiceTool.ServiceProvider.GetService<ICacheManager>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
        }

        public override void Intercept(IInvocation invocation)
        {
            // Şirket ID'sini al
            int companyId = -1;
            if (_companyContext != null)
            {
                companyId = _companyContext.GetCompanyId();
            }

            var methodName = string.Format($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}");
            var arguments = invocation.Arguments.ToList();
            
            // Önbellek anahtarına şirket ID'sini ekle
            var key = $"Company_{companyId}_{methodName}({string.Join(",", arguments.Select(x => x?.ToString() ?? "<Null>"))})";
            
            if (_cacheManager.IsAdd(key))
            {
                invocation.ReturnValue = _cacheManager.Get(key);
                return;
            }
            invocation.Proceed();
            _cacheManager.Add(key, invocation.ReturnValue, _duration);
        }
    }
}
