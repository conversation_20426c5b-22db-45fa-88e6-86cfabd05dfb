﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using DataAccess.Abstract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace Business.Concrete
{
    public class DebtPaymentManager : IDebtPaymentService
    {
        private readonly IDebtPaymentDal _debtPaymentDal;
        private readonly IRemainingDebtDal _remainingDebtDal;

        public DebtPaymentManager(IDebtPaymentDal debtPaymentDal, IRemainingDebtDal remainingDebtDal)
        {
            _debtPaymentDal = debtPaymentDal;
            _remainingDebtDal = remainingDebtDal;
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        public IResult Delete(int debtPaymentId)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    var debtPayment = _debtPaymentDal.Get(dp => dp.DebtPaymentID == debtPaymentId);
                    if (debtPayment == null)
                        return new ErrorResult(Messages.PaymentNotFound);

                    // İlgili RemainingDebt kaydını bul
                    var remainingDebt = _remainingDebtDal.Get(rd => rd.RemainingDebtID == debtPayment.RemainingDebtID);
                    if (remainingDebt == null)
                        return new ErrorResult(Messages.PaymentNotFound);

                    // Kalan borç tutarını güncelle (silinen ödeme miktarını geri ekle)
                    remainingDebt.RemainingAmount += debtPayment.PaidAmount;
                    remainingDebt.LastUpdateDate = DateTime.Now;
                    _remainingDebtDal.Update(remainingDebt);

                    // DebtPayment kaydını sil
                    _debtPaymentDal.Delete(debtPaymentId);

                    scope.Complete();
                    return new SuccessResult(Messages.PaymentDeleted);
                }
                catch (Exception ex)
                {
                    scope.Dispose();
                    return new ErrorResult($"Borç ödemesi silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }
    }


}
