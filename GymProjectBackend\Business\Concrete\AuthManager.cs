﻿using Business.Abstract;
using Business.Constants;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Extensions;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using Core.Utilities.Security.JWT;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class AuthManager : IAuthService
    {
        private IUserService _userService;
        private ITokenHelper _tokenHelper;
        private IUserDeviceService _userDeviceService;
        private IHttpContextAccessor _httpContextAccessor;
        private IUserLicenseService _userLicenseService;
        private IUserCompanyService _userCompanyService;
        private const int MAX_ACTIVE_DEVICES = 5;

        public AuthManager(
            IUserService userService,
            ITokenHelper tokenHelper,
            IUserDeviceService userDeviceService,
            IHttpContextAccessor httpContextAccessor,
            IUserLicenseService userLicenseService,
            IUserCompanyService userCompanyService)
        {
            _userService = userService;
            _tokenHelper = tokenHelper;
            _userDeviceService = userDeviceService;
            _httpContextAccessor = httpContextAccessor;
            _userLicenseService = userLicenseService;
            _userCompanyService = userCompanyService;
        }

        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IDataResult<User> Register(UserForRegisterDto userForRegisterDto, string password)
        {
            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(password, out passwordHash, out passwordSalt);
            var user = new User
            {
                Email = userForRegisterDto.Email,
                FirstName = userForRegisterDto.FirstName,
                LastName = userForRegisterDto.LastName,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsActive = true,
                CreationDate = DateTime.Now
            };
            _userService.Add(user);
            return new SuccessDataResult<User>(user, Messages.UserRegistered);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        public IDataResult<User> Login(UserForLoginDto userForLoginDto, string deviceInfo)
        {
            var userToCheck = _userService.GetByMail(userForLoginDto.Email);
            if (userToCheck == null)
            {
                return new ErrorDataResult<User>(null, Messages.UserNotFound);
            }

            if (!HashingHelper.VerifyPasswordHash(userForLoginDto.Password, userToCheck.PasswordHash, userToCheck.PasswordSalt))
            {
                return new ErrorDataResult<User>(null, Messages.PasswordError);
            }

            return new SuccessDataResult<User>(userToCheck, Messages.SuccessfulLogin);
        }

        [LogAspect]
        [PerformanceAspect(2)]
        public IDataResult<AccessToken> CreateAccessToken(User user, string deviceInfo)
        {
            // Get default claims from user service
            var defaultClaims = _userService.GetClaims(user);

            // Get additional claims from user licenses
            var licensedRoles = _userLicenseService.GetUserRoles(user.UserID).Data;

            // Combine all claims
            var allClaims = new List<OperationClaim>(defaultClaims);

            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            // Kullanıcının şirket ID'sini al
            var companyIdResult = _userCompanyService.GetUserCompanyId(user.UserID);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            // Token oluştur
            var accessToken = _tokenHelper.CreateToken(user, allClaims, companyId);
            var refreshToken = _tokenHelper.CreateRefreshToken(user);

            var userDevice = new UserDevice
            {
                UserId = user.UserID,
                DeviceInfo = deviceInfo,
                RefreshToken = refreshToken.Token,
                LastIpAddress = GetCurrentIPAddress(),
                RefreshTokenExpiration = refreshToken.Expiration,
                IsActive = true,
                CreatedAt = DateTime.Now,
                LastUsedAt = DateTime.Now
            };

            _userDeviceService.Add(userDevice);

            accessToken.RefreshToken = refreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.SuccessfulLogin);
        }

        public IDataResult<AccessToken> CreateAccessTokenWithRefreshToken(string refreshToken, string ipAddress, string deviceInfo)
        {
            var userDevice = _userDeviceService.GetByRefreshToken(refreshToken);
            if (!userDevice.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.InvalidRefreshToken);
            }

            var device = userDevice.Data;
            if (device.RefreshTokenExpiration <= DateTime.Now)
            {
                // Immediately revoke the expired token
                _userDeviceService.RevokeDevice(device.Id);
                return new ErrorDataResult<AccessToken>(Messages.ExpiredRefreshToken);
            }

            var userResult = _userService.GetById(device.UserId);
            if (!userResult.Success)
            {
                return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
            }

            // Get default claims from user service
            var defaultClaims = _userService.GetClaims(userResult.Data);

            // Get additional claims from user licenses
            var licensedRoles = _userLicenseService.GetUserRoles(userResult.Data.UserID).Data;

            // Combine all claims
            var allClaims = new List<OperationClaim>(defaultClaims);

            foreach (var role in licensedRoles)
            {
                if (!allClaims.Any(c => c.Name == role))
                {
                    allClaims.Add(new OperationClaim { Name = role });
                }
            }

            // Kullanıcının şirket ID'sini al
            var companyIdResult = _userCompanyService.GetUserCompanyId(userResult.Data.UserID);
            int companyId = companyIdResult.Success ? companyIdResult.Data : -1;

            var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
            var newRefreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

            device.RefreshToken = newRefreshToken.Token;
            device.LastIpAddress = ipAddress;
            device.LastUsedAt = DateTime.Now;
            device.DeviceInfo = deviceInfo;

            _userDeviceService.Update(device);

            accessToken.RefreshToken = newRefreshToken.Token;
            return new SuccessDataResult<AccessToken>(accessToken, Messages.TokensRefreshed);
        }

        public IResult RevokeRefreshToken(string refreshToken)
        {
            var userDevice = _userDeviceService.GetByRefreshToken(refreshToken);
            if (!userDevice.Success)
            {
                return new ErrorResult(Messages.InvalidRefreshToken);
            }

            return _userDeviceService.RevokeDevice(userDevice.Data.Id);
        }

        public IResult RevokeAllDevices(int userId)
        {
            var currentRefreshToken = _httpContextAccessor.HttpContext?.Request?.Headers["X-Refresh-Token"].ToString();
            return _userDeviceService.RevokeAllDevicesExceptCurrent(userId, currentRefreshToken);
        }

        public IResult RevokeDevice(int deviceId)
        {
            return _userDeviceService.RevokeDevice(deviceId);
        }

    public IDataResult<List<UserDeviceDto>> GetUserDevices(int userId)
    {
        var devices = _userDeviceService.GetActiveDevicesByUserId(userId);
        if (!devices.Success)
        {
            return new ErrorDataResult<List<UserDeviceDto>>(devices.Message);
        }

        var deviceDtos = devices.Data.Select(d => new UserDeviceDto
        {
            Id = d.Id,
            DeviceInfo = d.DeviceInfo,
            LastIpAddress = d.LastIpAddress,
            CreatedAt = d.CreatedAt,
            LastUsedAt = d.LastUsedAt
        }).ToList();

        return new SuccessDataResult<List<UserDeviceDto>>(deviceDtos);
    }

    [LogAspect]
    [PerformanceAspect(3)]
    public IDataResult<AccessToken> ChangeCompany(int userId, int companyId, string deviceInfo)
    {
        // Kullanıcıyı kontrol et
        var userResult = _userService.GetById(userId);
        if (!userResult.Success)
        {
            return new ErrorDataResult<AccessToken>(Messages.UserNotFound);
        }

        // Kullanıcının bu şirkete erişim yetkisi olup olmadığını kontrol et
        var userCompanies = _userCompanyService.GetUserCompanies(userId);
        if (!userCompanies.Success)
        {
            return new ErrorDataResult<AccessToken>(Messages.UserCompanyNotFound);
        }

        if (!userCompanies.Data.Any(uc => uc.CompanyId == companyId && uc.IsActive == true))
        {
            return new ErrorDataResult<AccessToken>(Messages.UserCompanyAccessDenied);
        }

        // Kullanıcının aktif şirketini güncelle
        var updateResult = _userCompanyService.UpdateActiveCompany(userId, companyId);
        if (!updateResult.Success)
        {
            return new ErrorDataResult<AccessToken>(updateResult.Message);
        }

        // Kullanıcının rollerini al
        var defaultClaims = _userService.GetClaims(userResult.Data);
        var licensedRoles = _userLicenseService.GetUserRoles(userId).Data;

        // Tüm rolleri birleştir
        var allClaims = new List<OperationClaim>(defaultClaims);
        foreach (var role in licensedRoles)
        {
            if (!allClaims.Any(c => c.Name == role))
            {
                allClaims.Add(new OperationClaim { Name = role });
            }
        }

        // Yeni token oluştur
        var accessToken = _tokenHelper.CreateToken(userResult.Data, allClaims, companyId);
        var refreshToken = _tokenHelper.CreateRefreshToken(userResult.Data);

        // Kullanıcının cihaz bilgilerini güncelle
        var userDevice = new UserDevice
        {
            UserId = userId,
            DeviceInfo = deviceInfo,
            RefreshToken = refreshToken.Token,
            LastIpAddress = GetCurrentIPAddress(),
            RefreshTokenExpiration = refreshToken.Expiration,
            IsActive = true,
            CreatedAt = DateTime.Now,
            LastUsedAt = DateTime.Now
        };

        _userDeviceService.Add(userDevice);

        accessToken.RefreshToken = refreshToken.Token;
        return new SuccessDataResult<AccessToken>(accessToken, Messages.CompanyChanged);
    }

    private string GetCurrentIPAddress()
    {
        return _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString();
    }
    public IResult UserExists(string email)
    {
        if (_userService.GetByMail(email) != null)
        {
            return new ErrorResult(Messages.UserAlreadyExists);
        }
        return new SuccessResult();
    }
    }
}
