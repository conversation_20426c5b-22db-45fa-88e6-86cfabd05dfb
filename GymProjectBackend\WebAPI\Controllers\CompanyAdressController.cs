﻿using Business.Abstract;
using Entities.Concrete;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace WebAPI.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class CompanyAdressController : ControllerBase
    {
        ICompanyAdressService _companyAdressService;

        public CompanyAdressController(ICompanyAdressService companyAdressService)
        {
            _companyAdressService = companyAdressService;
        }
        [HttpGet("getall")]
        public IActionResult GetAll()
        {
            var result = _companyAdressService.GetAll();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
        [HttpPost("add")]
        public IActionResult Add(CompanyAdress companyAdress)
        {
            var result = _companyAdressService.Add(companyAdress);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpDelete("delete")]
        public IActionResult Delete(int id)
        {
            var result = _companyAdressService.Delete(id);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }
        [HttpPost("update")]
        public IActionResult Update(CompanyAdress companyAdress)
        {
            var result = _companyAdressService.Update(companyAdress);
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest();
        }

        [HttpGet("getcompanyadressdetails")]
        public IActionResult GetCompanyAdressDetails()
        {
            var result = _companyAdressService.GetCompanyAdressDetails();
            if (result.Success)
            {
                return Ok(result);
            }
            return BadRequest(result);
        }
    }
}
