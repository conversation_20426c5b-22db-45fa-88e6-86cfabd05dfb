﻿using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace Business.Abstract
{
    public interface IMemberService
    {
        IDataResult<MemberDetailWithHistoryDto> GetMemberDetailById(int memberId);
        IDataResult<List<MemberBirthdayDto>> GetUpcomingBirthdays(int days);
        IDataResult<List<Member>> GetAll();
        IResult Add(Member member);
        IResult Update(Member member);
        IResult Delete(int id);
        IDataResult<List<MembeFilterDto>> GetMemberDetails();
        IDataResult<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistory();
        IDataResult<List<MemberRemainingDayDto>> GetMemberRemainingDay();
        IDataResult<MemberDetailDto> GetMemberRemainingDaysForScanNumber(string scanNumber);
        IDataResult<List<Member>> GetByMemberId(int memberid);
        IDataResult<List<GetActiveMemberDto>> GetActiveMembers();
        IDataResult<GetMemberQRByPhoneNumberDto> GetMemberQRByPhoneNumber(string phoneNumber);
        IDataResult<List<MemberEntryDto>> GetTodayEntries(DateTime date);
        IDataResult<PaginatedResult<Member>> GetAllPaginated(MemberPagingParameters parameters);
        IDataResult<PaginatedResult<MemberFilter>> GetMemberDetailsPaginated(MemberPagingParameters parameters);
        IDataResult<int> GetTotalActiveMembers();
        IDataResult<Dictionary<string, int>> GetActiveMemberCounts();
        IDataResult<Dictionary<string, int>> GetBranchCounts();
        IDataResult<List<MemberEntryDto>> GetMemberEntriesByName(string name);
    }
}
