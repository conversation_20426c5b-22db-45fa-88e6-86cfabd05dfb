{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\canlı proje\\GymProjectBackend\\Core\\Core.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\canlı proje\\GymProjectBackend\\Core\\Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\canlı proje\\GymProjectBackend\\Core\\Core.csproj", "projectName": "Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\canlı proje\\GymProjectBackend\\Core\\Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\canlı proje\\GymProjectBackend\\Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Autofac": {"target": "Package", "version": "[8.0.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[9.0.0, )"}, "Autofac.Extras.DynamicProxy": {"target": "Package", "version": "[7.1.0, )"}, "Azure.Identity": {"target": "Package", "version": "[1.13.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.9.0, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.4, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[9.0.0, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[7.5.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Formats.Asn1": {"target": "Package", "version": "[9.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[7.5.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.103/PortableRuntimeIdentifierGraph.json"}}}}}